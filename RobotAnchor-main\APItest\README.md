# 🤖 Dify AI 商品话术生成工具

一个功能完整的GUI工具，用于调用Dify AI工作流接口生成商品介绍话术。采用streaming模式彻底解决504超时问题，支持商品信息管理、话术生成、随机播放等功能。

## ✨ 功能特点

### 🖥️ 图形化界面
- **直观易用**：完整的GUI界面，无需命令行操作
- **实时反馈**：显示API调用进度和执行状态
- **多线程处理**：后台API调用，界面不会冻结

### 🌊 流式API调用
- **streaming模式**：使用SSE流式响应，彻底避免504超时
- **无超时限制**：支持任意长时间的工作流执行
- **实时进度显示**：监听工作流各节点执行状态

### 📦 商品信息管理
- **信息保存**：支持保存商品信息到本地文件
- **历史查看**：查看已保存的商品信息列表
- **智能验证**：必填字段验证和数据格式检查

### 🎯 话术生成与管理
- **自动生成**：调用Dify AI工作流生成商品介绍话术
- **智能文件命名**：使用商品名称自动生成文件名
- **批量保存**：自动保存生成的话术到txt文件

### 🎲 随机播放功能
- **智能抽取**：按文件顺序从每个话术文件随机抽取一条组成播放列表
- **循环播放**：支持连续循环播放，可随时停止
- **实时显示**：在输出区域实时显示播放内容

## 📋 项目文件结构

```
APItest/
├── gui_api_test.py              # 主要GUI工具
├── barrage_reply_test.py        # 弹幕回复测试工具（独立）
├── requirements.txt             # 项目依赖
├── README.md                    # 项目说明文档
├── SOLUTION_SUMMARY.md          # 504错误解决方案说明
├── generated_scripts/           # 生成的话术文件目录（自动创建）
└── product_info_list.txt        # 商品信息保存文件（自动创建）
```

## 🚀 快速开始

### 1. 环境要求

- **Python版本**：3.7+
- **操作系统**：Windows / Linux / macOS
- **网络要求**：能够访问api.dify.ai

### 2. 安装依赖

```bash
# 克隆或下载项目到本地
cd APItest

# 安装依赖
pip install -r requirements.txt
```

或者直接安装：

```bash
pip install requests
```

### 3. 运行GUI工具

```bash
python gui_api_test.py
```

## 📖 使用指南

### 🖥️ GUI界面操作

#### 1. 商品信息输入
- **商品名称**：必填，用于文件命名
- **价格**：商品价格信息
- **描述**：商品简短描述
- **详细信息**：商品详细说明（支持多行）
- **购买须知**：购买注意事项（支持多行）
- **购买限制**：购买限制条件

#### 2. 功能按钮说明
- **💾 保存商品信息**：将当前输入的商品信息保存到本地文件
- **🚀 生成话术**：调用Dify AI接口生成商品介绍话术
- **📋 查看商品列表**：查看已保存的商品信息历史记录

#### 3. 随机播放控制
- **▶️ 开始随机播放**：开始随机播放已生成的话术
- **⏹️ 停止播放**：停止当前播放

### 🎯 话术生成流程

1. **填写商品信息**：在GUI界面输入完整的商品信息
2. **点击生成话术**：系统将调用Dify AI接口
3. **实时查看进度**：在输出区域查看API调用进度
4. **自动保存文件**：生成的话术自动保存到`generated_scripts/`目录
5. **文件命名规则**：使用商品名称作为文件名，自动清理特殊字符

### 🎲 随机播放功能

**播放逻辑**：
1. 扫描`generated_scripts/`目录下的所有txt文件
2. 按文件名排序，从每个文件中随机抽取一条话术
3. 组成播放列表，按顺序循环播放
4. 每轮播放完成后重新抽取，形成新的播放列表

**使用场景**：
- 直播间话术播放
- 销售培训材料
- 商品介绍展示

## 🔧 独立工具

### 弹幕回复测试工具

项目还包含一个独立的弹幕回复测试工具：

```bash
python barrage_reply_test.py
```

**功能**：
- 测试Dify AI弹幕回复工作流
- 提取Reply参数内容
- 支持streaming模式避免超时

## ⚙️ API配置说明

### Dify AI接口信息
- **URL**：`https://api.dify.ai/v1/workflows/run`
- **认证方式**：Bearer Token
- **响应模式**：streaming（流式）
- **超时设置**：连接30秒，读取300秒

### 核心优势
根据Dify API官方文档：
- **blocking模式**：由于Cloudflare限制，请求会在100秒超时后中断（导致504错误）
- **streaming模式**：基于SSE（Server-Sent Events）实现流式返回，**无超时限制**

## 🛠️ 故障排除

### 常见问题

#### 1. 504超时错误
**原因**：使用了blocking模式  
**解决方案**：本工具已使用streaming模式，无此问题

#### 2. 网络连接错误
**症状**：显示"连接错误"  
**解决方案**：
- 检查网络连接
- 确认能访问api.dify.ai
- 检查防火墙设置

#### 3. 文件保存失败
**可能原因**：
- 磁盘空间不足
- 文件权限问题
- 商品名称包含特殊字符

**解决方案**：
- 检查磁盘空间
- 确保程序有写入权限
- 工具会自动清理文件名中的特殊字符

#### 4. GUI界面无响应
**原因**：API调用时间较长  
**说明**：这是正常现象，工具使用多线程处理，请耐心等待

#### 5. 随机播放无内容
**原因**：`generated_scripts/`目录为空  
**解决方案**：先生成一些话术文件

### 调试模式

如需查看详细的API调用信息，可以查看输出区域的实时日志。

## 🔄 更新日志

### v2.0.0 (当前版本)
- ✅ 完整的GUI界面
- ✅ 商品信息管理功能
- ✅ 随机播放功能优化
- ✅ 文件名清理逻辑完善
- ✅ 多线程API调用
- ✅ 删除冗余的api_test.py文件

### v1.0.0
- ✅ 基础API调用功能
- ✅ streaming模式支持
- ✅ SSE响应处理

## 📞 技术支持

如遇到问题，请检查：
1. Python版本是否符合要求（3.7+）
2. 依赖是否正确安装
3. 网络连接是否正常
4. API Token是否有效

## 📄 许可证

本项目仅供学习和测试使用。


